# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a Python-based web scraping application that provides a GUI for downloading image galleries from mxd009.cc. The application uses PySide6 for the GUI and requests/BeautifulSoup for web scraping.

## Architecture

The codebase is contained in a single file `1.py` with the following main components:

### Core Functions

- `submit_search()`: Submits search requests and handles redirects
- `get_total_count()`: Extracts total gallery count from pages
- `parse_gallery_items_from_root()`: Parses gallery items from search results
- `crawl_single_gallery()`: Crawls individual gallery pages
- `crawl_all_pages()`: Handles pagination for complete results

### GUI Classes

- `GalleryCrawler`: Main application window with search, display, and download functionality
- `DownloadWorker`: QThread for background downloading
- `ThumbnailViewer`: Dialog for displaying image previews

### Key Features

- Search by keywords or direct URLs
- Batch downloading with progress tracking
- Thumbnail preview functionality
- Multi-threaded downloads to avoid UI blocking

## Development Commands

### Running the Application

```bash
python3 1.py
```

### Dependencies

The application requires the following Python packages:

- PySide6 (GUI framework)
- requests (HTTP requests)
- beautifulsoup4 (HTML parsing)
- logging (built-in)

Install dependencies:

```bash
pip3 install PySide6 requests beautifulsoup4
```

## Code Patterns

### Error Handling

- Uses try-except blocks for network requests
- Graceful degradation when elements are not found
- Timeout handling for requests (10-15 seconds)

### Threading

- Uses QThread for background operations
- Threading for image loading to prevent UI blocking
- Custom event system for thread-safe UI updates

### URL Handling

- Supports both keyword searches and direct URL inputs
- Handles different URL patterns (search results vs. gallery URLs)
- Uses urljoin for proper URL construction

## Configuration

### Constants

- `BASE_URL`: Main website URL (https://www.mxd009.cc)
- Default headers configured to mimic browser behavior
- Timeout values set for network requests

### File Structure

Downloaded images are organized as:

```
[author]/[album_title]/[image_files]
```

## Important Notes

- The application is designed for a specific website structure
- Uses session management for consistent requests
- Implements rate limiting through random delays
- Handles both logged-in and non-logged-in states of the target website
